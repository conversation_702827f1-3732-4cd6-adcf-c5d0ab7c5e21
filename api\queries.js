import { gql } from '@apollo/client';

export const GET_ORGANIZATIONS = gql`
  query {
    organizations {
      hiveId
      name
      foundingDate
      members {
        hiveId
        firstName
        lastName
      }
      ownsVehicle {
        hiveId
        type
        make
        model
        color
      }
      parentOrganization {
        hiveId
        name
      }
      childOrganizations {
        hiveId
        name
      }
    }
  }
`;

export const GET_PEOPLE = gql`
  query {
    people {
      hiveId
      firstName
      lastName
      dateOfBirth
      hasAgent {
        hiveId
        username
      }
      hasInformant {
        hiveId
        codeName
      }
      memberOf {
        hiveId
        name
      }
      suspectIn {
        hiveId
        title
      }
      victimIn {
        hiveId
        title
      }
      witnessIn {
        hiveId
        title
      }
      ownsVehicle {
        hiveId
        type
        make
        model
        color
      }
    }
  }
`;

export const GET_CASES = gql`
  query {
    cases {
      hiveId
      title
      creationDate
      status
      suspects {
        hiveId
        firstName
        lastName
      }
      victims {
        hiveId
        firstName
        lastName
      }
      witnesses {
        hiveId
        firstName
        lastName
      }
    }
  }
`; 

export const GET_AGENTS = gql`
  query {
    agents {
      hiveId
      username
      role
      backingPerson {
        hiveId
        firstName
        lastName
      }
      handlesInformant {
        hiveId
        codeName
      }
    }
  }
`; 

export const GET_INFORMANTS = gql`
  query {
    informants {
      hiveId
      codeName
      backingPerson {
        hiveId
        firstName
        lastName
      }
      handlerAgent {
        hiveId
        username
      }
    }
  }
`; 

export const GET_VEHICLES = gql`
  query GetVehicles {
    vehicles {
      hiveId
      type
      make
      model
      color
      owner {
        __typename
        ... on Person {
          hiveId
          firstName
          lastName
        }
        ... on Organization {
          hiveId
          name
        }
      }
    }
  }
`; 
