import React, { useState } from 'react';
import { useQuery, useMutation } from '@apollo/client';
import toast from 'react-hot-toast';
import { GET_INFORMANTS, GET_AGENTS } from '../../../api/queries';
import { DELETE_INFORMANT, SET_INFORMANT_HANDLER } from '../../../api/mutations';
import CreateInformantModal from './CreateInformant';
import SetInformantHandlerModal from './SetInformantHandlerModal';
import '../../../css/shared.css'; 

export default function Informants() {
  const { loading, error, data, refetch } = useQuery(GET_INFORMANTS);
  const [copiedId, setCopiedId] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSetHandlerModalOpen, setIsSetHandlerModalOpen] = useState(false);
  const [currentInformant, setCurrentInformant] = useState(null);

  const [deleteInformantMutation] = useMutation(DELETE_INFORMANT, {
    onCompleted: () => {
      refetch();
      toast.success('Informant deleted successfully!');
    },
    onError: (deleteError) => {
      console.error('Error deleting informant:', deleteError);
      toast.error(`Error deleting informant: ${deleteError.message}`);
    }
  });

  const [setInformantHandlerMutation] = useMutation(SET_INFORMANT_HANDLER, {
    onCompleted: () => {
      refetch();
      setIsSetHandlerModalOpen(false);
      setCurrentInformant(null);
      toast.success('Informant handler set successfully!');
    },
    onError: (setError) => {
      console.error('Error setting informant handler:', setError);
      toast.error(`Error setting informant handler: ${setError.message}`);
    }
  });

  const handleDeleteInformant = (informantId, informantName) => {
    if (window.confirm(`Are you sure you want to delete informant "${informantName}" (ID: ${informantId})? This action cannot be undone.`)) {
      deleteInformantMutation({ variables: { hiveId: informantId } });
    }
  };

  const copyToClipboard = (id) => {
    navigator.clipboard.writeText(id);
    setCopiedId(id);
    setTimeout(() => setCopiedId(null), 2000);
  };

  if (loading) return <div style={{ padding: '1rem' }}>Loading informants...</div>;
  if (error) return <div style={{ padding: '1rem' }}>Error: {error.message}</div>;

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  const openSetHandlerModal = (informant) => {
    setCurrentInformant(informant);
    setIsSetHandlerModalOpen(true);
  };

  const closeSetHandlerModal = () => {
    setIsSetHandlerModalOpen(false);
    setCurrentInformant(null);
  };

  return (
    <div style={{ padding: '1rem' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h1>Informants</h1>
        <button onClick={openModal} className="button-primary">
          Create Informant
        </button>
      </div>

      <CreateInformantModal isOpen={isModalOpen} onClose={closeModal} />

      {currentInformant && (
        <SetInformantHandlerModal
          isOpen={isSetHandlerModalOpen}
          onClose={closeSetHandlerModal}
          informant={currentInformant}
          onSubmitSetHandler={(handlerData) => setInformantHandlerMutation({
            variables: {
              informantHiveId: currentInformant.hiveId,
              agentHiveId: handlerData.handlerHiveId
            }
          })}
        />
      )}

      <div style={{ marginTop: '2rem' }}>
        {data?.informants?.map((informant) => (
          <div 
            key={informant.hiveId}
            style={{
              padding: '1rem',
              marginBottom: '1rem',
              borderRadius: '4px',
              backgroundColor: '#222',
              position: 'relative'
            }}
          >
            <button 
              onClick={() => handleDeleteInformant(informant.hiveId, informant.codeName)}
              className="button-delete"
              style={{ position: 'absolute', top: '12px', right: '16px' }}
              title="Delete Informant"
            >
              Delete
            </button>
            <button
              onClick={() => openSetHandlerModal(informant)}
              style={{ position: 'absolute', top: '12px', right: '78px' }}
              className="button-utility"
            >
              Set Handler
            </button>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginRight: '150px' }}>
              <h3>{informant.codeName}</h3>
              <span 
                style={{fontSize: '0.8em',color: '#909090',cursor: 'pointer',userSelect: 'none'}}
                onClick={() => copyToClipboard(informant.hiveId)}
                title="Click to copy Hive ID"
              >
                {informant.hiveId}
                {copiedId === informant.hiveId && ' ✓'}
              </span>
            </div>
            <hr></hr>
            <div style={{ display: 'flex', gap: '1rem' }}>
              {informant.backingPerson && informant.backingPerson.length > 0 && (
                <div style={{
                  flex: 1,
                  padding: '1rem',
                  backgroundColor: '#2a2a2a',
                  borderRadius: '4px'
                }}>
                  <div>
                    <strong>Backing Person:</strong>
                    {informant.backingPerson.map(person => (
                      <div key={person.hiveId} style={{color: '#909090'}}>
                        <p style={{margin: 0}}>
                          {person.firstName} {person.lastName}
                        </p>
                        <p 
                          style={{fontSize: '0.8em', margin: 0, cursor: 'pointer'}} 
                          onClick={() => copyToClipboard(person.hiveId)} 
                          title="Copy person Hive ID"
                        >
                          Hive ID: {person.hiveId}
                          {copiedId === person.hiveId && ' ✓'}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              <div style={{
                flex: 1,
                padding: '1rem',
                backgroundColor: '#2a2a2a',
                borderRadius: '4px'
              }}>
                <div>
                  <strong>Handled By:</strong>
                  {informant.handlerAgent && informant.handlerAgent.length > 0 ? (
                    <ul>
                      {informant.handlerAgent.map(agent => {
                        if (!agent || !agent.hiveId) return <li key={Math.random()} style={{marginLeft: '18px', color: '#909090'}}>Invalid agent data</li>;
                        return (
                          <li style={{marginLeft: '18px', color: '#ffda94'}} key={agent.hiveId}>
                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                              <span>Agent: {agent.username}</span>
                              <span 
                                style={{fontSize: '0.8em',color: '#909090',cursor: 'pointer',userSelect: 'none'}}
                                onClick={() => copyToClipboard(agent.hiveId)}
                                title={`Copy Agent ID`}
                              >
                                {agent.hiveId}
                                {copiedId === agent.hiveId && ' ✓'}
                              </span>
                            </div>
                          </li>
                        );
                      })}
                    </ul>
                  ) : (
                    <p style={{color: '#909090'}}>N/A</p>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
} 