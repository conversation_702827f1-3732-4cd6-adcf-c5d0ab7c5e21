.modalBackdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modalContent {
  background-color: #222; 
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  color: #e0e0e0; 
  width: 90%;
  max-width: 500px;
  position: relative;
}

.modalHeader {
  border-bottom: 1px solid #444; 
  padding-bottom: 10px;
  margin-bottom: 20px;
  font-size: 1.5em;
  color: #ffffff;
}

.modalBody {
  margin-bottom: 20px;
}

.modalFooter {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.modalContent label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #c7c7c7; 
}

.darkInput {
  width: 100%;
  padding: 10px;
  margin-bottom: 20px;
  border-radius: 4px;
  border: 1px solid #555;
  background-color: #3b3b3b; 
  color: #e0e0e0;
  font-size: 14px;
  box-sizing: border-box; 
}

.darkInput::placeholder {
  color: #888; 
}

.darkInput:focus {
  outline: none;
  box-shadow: 0 0 0 1.5px #835c0c;
}

.modalContent button {
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.modalContent button.primary {
  background-color: #3a3a3a;
  padding: 10px 15px;
  color: white;
}

.modalContent button.primary:hover {
  background-color: #444;
}

.modalContent button.secondary {
  background-color: #693030;
  color: white;
  padding: 10px 15px;
}

.modalContent button.secondary:hover {
  background-color: #702424;
}

.modalCloseButton {
  position: absolute;
  top: 15px;
  right: 15px;
  background: none;
  padding: 10px 10px;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #ccc;
}

.modalCloseButton:hover {
  color: #fff;
}
