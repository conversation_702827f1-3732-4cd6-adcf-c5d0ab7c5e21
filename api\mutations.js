import { gql } from '@apollo/client';

export const LOGIN_MUTATION = gql`
  mutation Login($username: String!, $password: String!) {
    login(username: $username, password: $password) {
      token
      agent {
        hiveId
        username
        role
      }
    }
  }
`;

export const CREATE_ORGANIZATION = gql`
  mutation CreateOrganization($name: String!, $foundingDate: Date!) {
    createOrganization(name: $name, foundingDate: $foundingDate) {
      hiveId
      name
      foundingDate
    }
  }
`;

export const CREATE_PERSON = gql`
  mutation CreatePerson($firstName: String!, $lastName: String!, $dateOfBirth: Date!) {
    createPerson(firstName: $firstName, lastName: $lastName, dateOfBirth: $dateOfBirth) {
      hiveId
      firstName
      lastName
      dateOfBirth
    }
  }
`;

export const CREATE_CASE = gql`
  mutation CreateCase($title: String!) {
    createCase(title: $title) {
      hiveId
      title
      creationDate
    }
  }
`;

export const CREATE_AGENT = gql`
  mutation CreateAgent(
    $username: String!
    $password: String!
    $role: String!
    $backingPersonHiveId: ID!
  ) {
    createAgent(
      username: $username
      password: $password
      role: $role
      backingPersonHiveId: $backingPersonHiveId
    ) {
      hiveId
      username
      role
      backingPerson {
        hiveId
        firstName
        lastName
      }
    }
  }
`;

export const CREATE_INFORMANT = gql`
  mutation CreateInformant($codeName: String!, $backingPersonHiveId: ID!) {
    createInformant(codeName: $codeName, backingPersonHiveId: $backingPersonHiveId) {
      hiveId
      codeName
      backingPerson {
        hiveId
        firstName
        lastName
      }
    }
  }
`;

export const DELETE_PERSON = gql`
  mutation DeletePerson($hiveId: ID!) {
    deletePerson(hiveId: $hiveId) {
      hiveId
      deleted
    }
  }
`;

export const DELETE_ORGANIZATION = gql`
  mutation DeleteOrganization($hiveId: ID!) {
    deleteOrganization(hiveId: $hiveId) {
      hiveId
      deleted
    }
  }
`;

export const DELETE_CASE = gql`
  mutation DeleteCase($hiveId: ID!) {
    deleteCase(hiveId: $hiveId) {
      hiveId
      deleted
    }
  }
`;

export const DELETE_AGENT = gql`
  mutation DeleteAgent($hiveId: ID!) {
    deleteAgent(hiveId: $hiveId) {
      hiveId
      deleted
    }
  }
`;

export const DELETE_INFORMANT = gql`
  mutation DeleteInformant($hiveId: ID!) {
    deleteInformant(hiveId: $hiveId) {
      hiveId
      deleted
    }
  }
`;

export const DELETE_VEHICLE = gql`
  mutation DeleteVehicle($hiveId: ID!) {
    deleteVehicle(hiveId: $hiveId) {
      hiveId
      deleted
    }
  }
`;

export const CREATE_VEHICLE = gql`
  mutation CreateVehicle($type: VehicleType!, $make: String!, $model: String!, $color: String!) {
    createVehicle(type: $type, make: $make, model: $model, color: $color) {
      hiveId
      type
      make
      model
      color
    }
  }
`;

export const SET_VEHICLE_OWNER = gql`
  mutation SetVehicleOwner($vehicleHiveId: ID!, $ownerHiveId: ID!) {
    setVehicleOwner(vehicleHiveId: $vehicleHiveId, ownerHiveId: $ownerHiveId) {
      hiveId
      type
      make
      model
      color
      owner {
        __typename
        ... on Person {
          hiveId
          firstName
          lastName
        }
        ... on Organization {
          hiveId
          name
          foundingDate
        }
      }
    }
  }
`;

export const SET_INFORMANT_HANDLER = gql`
  mutation SetInformantHandler($informantHiveId: ID!, $agentHiveId: ID!) {
    setInformantHandler(informantHiveId: $informantHiveId, agentHiveId: $agentHiveId) {
      hiveId
      codeName
      handlerAgent {
        hiveId
        username
      }
    }
  }
`;

export const SET_ORGANIZATION_PARENT = gql`
  mutation SetOrganizationParent($childOrganizationHiveId: ID!, $parentOrganizationHiveId: ID!) {
    setOrganizationParent(childOrganizationHiveId: $childOrganizationHiveId, parentOrganizationHiveId: $parentOrganizationHiveId) {
      hiveId
      name
      parentOrganization {
        hiveId
        name
      }
      childOrganizations {
        hiveId
        name
      }
    }
  }
`;
