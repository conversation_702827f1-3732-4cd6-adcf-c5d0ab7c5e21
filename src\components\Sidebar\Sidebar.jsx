import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Home, Folder, Users, AudioLines, Building2, Shield, Truck, Target } from 'lucide-react';
import SearchBar from './comps/SearchBar/SearchBar';
import BottomPanel from './comps/BottomPanel';
import ProfileCard from './comps/ProfileCard';
import styles from '../../../css/sidebar.module.css';

const navItems = [
  { path: '/',               icon: Home,            label: 'Home'           },
  { path: '/organizations',  icon: Building2,       label: 'Organizations'  },
  { path: '/people',         icon: Users,           label: 'People'         },
  { path: '/cases',          icon: Folder,          label: 'Cases'          },
  { path: '/agents',         icon: Shield,          label: 'Agents'         },
  { path: '/informants',     icon: AudioLines,      label: 'Informants'     },
  { path: '/vehicles',       icon: Truck,           label: 'Vehicles'       },
  { path: '/operations',     icon: Target,          label: 'Operations'     },
];

export default function Sidebar() {
  const [isCollapsed, setIsCollapsed] = useState(() => 
    JSON.parse(localStorage.getItem('sidebarCollapsed') || 'false')
  );
  const navigate = useNavigate();
  const location = useLocation();

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
    localStorage.setItem('sidebarCollapsed', !isCollapsed);
  };

  return (
    <div className={`${styles.sidebar} ${isCollapsed ? styles.collapsed + ' sidebar-is-collapsed' : ''}`}>
      <div className={styles['logo-container']}>
        <img style={{width: '40px', height: '40px'}} src="https://kappa.lol/Q5C_rG" alt="Logo" />
        {!isCollapsed && <h1 className={styles['sidebar-title']}>Corvidan</h1>}
      </div>

      <SearchBar isCollapsed={isCollapsed} handleCollapse={toggleCollapse} />

      <nav className={styles['nav-items']}>
        {navItems.map(({ path, icon: Icon, label }) => (
          <button
            key={path}
            className={`${styles['nav-button']} ${location.pathname === path ? styles.active : ''}`}
            onClick={() => navigate(path)}
          >
            <Icon size={20} />
            {!isCollapsed && <span>{label}</span>}
          </button>
        ))}
      </nav>

      <ProfileCard isCollapsed={isCollapsed} userName="Internal User" status="Online" rank="Keystone Admin" />
      <BottomPanel isCollapsed={isCollapsed} handleCollapse={toggleCollapse}/>

    </div>
  );
}
