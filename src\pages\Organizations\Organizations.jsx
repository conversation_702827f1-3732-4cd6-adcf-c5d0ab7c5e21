// src/pages/Organizations/Organizations.jsx
import React, { useState } from 'react';
import { useQuery, useMutation } from '@apollo/client';
import toast from 'react-hot-toast';
import { GET_ORGANIZATIONS } from '../../../api/queries';
import { DELETE_ORGANIZATION, SET_ORGANIZATION_PARENT } from '../../../api/mutations';
import CreateOrganizationModal from './CreateOrganization';
import SetParentOrganizationModal from './SetParentOrganizationModal';
import '../../../css/shared.css'; // Import shared CSS

export default function Organizations() {
  const { loading, error, data, refetch } = useQuery(GET_ORGANIZATIONS);
  const [copiedId, setCopiedId] = useState(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isSetParentModalOpen, setIsSetParentModalOpen] = useState(false);
  const [selectedOrg, setSelectedOrg] = useState(null);

  const [deleteOrganizationMutation] = useMutation(DELETE_ORGANIZATION, {
    onCompleted: () => {
      refetch();
      toast.success('Organization deleted successfully!');
    },
    onError: (deleteError) => {
      console.error('Error deleting organization:', deleteError);
      toast.error(`Error deleting organization: ${deleteError.message}`);
    }
  });

  const [setOrganizationParentMutation] = useMutation(SET_ORGANIZATION_PARENT, {
    onCompleted: () => {
      refetch();
      toast.success('Parent organization set successfully!');
      closeSetParentModal();
    },
    onError: (setError) => {
      console.error('Error setting parent organization:', setError);
      toast.error(`Error setting parent organization: ${setError.message}`);
    }
  });

  const handleDeleteOrganization = (orgId, orgName) => {
    if (window.confirm(`Are you sure you want to delete ${orgName} (ID: ${orgId})? This action cannot be undone.`)) {
      deleteOrganizationMutation({ variables: { hiveId: orgId } });
    }
  };

  const copyToClipboard = (id) => {
    navigator.clipboard.writeText(id);
    setCopiedId(id);
    setTimeout(() => setCopiedId(null), 2000);
  };

  if (loading) return <div style={{ padding: '1rem' }}>Loading organizations...</div>;
  if (error) return <div style={{ padding: '1rem' }}>Error: {error.message}</div>;

  const openCreateModal = () => setIsCreateModalOpen(true);
  const closeCreateModal = () => setIsCreateModalOpen(false);

  const openSetParentModal = (org) => {
    setSelectedOrg(org);
    setIsSetParentModalOpen(true);
  };
  const closeSetParentModal = () => {
    setSelectedOrg(null);
    setIsSetParentModalOpen(false);
  };

  const handleSetParentSubmit = ({ parentOrganizationHiveId }) => {
    if (selectedOrg) {
      setOrganizationParentMutation({
        variables: {
          childOrganizationHiveId: selectedOrg.hiveId,
          parentOrganizationHiveId: parentOrganizationHiveId,
        },
      });
    }
  };

  return (
    <div style={{ padding: '1rem' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h1>Organizations</h1>
        <button onClick={openCreateModal} className="button-primary">
          Create Organization
        </button>
      </div>

      <CreateOrganizationModal isOpen={isCreateModalOpen} onClose={closeCreateModal} />
      {selectedOrg && (
        <SetParentOrganizationModal
          isOpen={isSetParentModalOpen}
          onClose={closeSetParentModal}
          organization={selectedOrg}
          onSubmitSetParent={handleSetParentSubmit}
        />
      )}

      <div style={{ marginTop: '2rem' }}>
        {data.organizations.map((org) => (
          <div 
            key={org.hiveId}
            style={{
              padding: '1rem',
              marginBottom: '1rem',
              borderRadius: '4px',
              backgroundColor: '#222',
              position: 'relative'
            }}
          >
            <div style={{ position: 'absolute', top: '12px', right: '16px', display: 'flex', gap: '8px' }}>
                <button
                    onClick={() => openSetParentModal(org)}
                    className="button-utility"
                    title="Set Parent Organization"
                >
                    Set Parent
                </button>
                <button 
                onClick={() => handleDeleteOrganization(org.hiveId, org.name)}
                className="button-delete"
                title="Delete Organization"
                >
                Delete
                </button>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginRight: '150px' }}>
              <h3>{org.name}</h3>
              <span 
                style={{fontSize: '0.8em',color: '#909090',cursor: 'pointer',userSelect: 'none'}}
                onClick={() => copyToClipboard(org.hiveId)}
                title="Click to copy Hive ID"
              >
                {org.hiveId}
                {copiedId === org.hiveId && ' ✓'}
              </span>
            </div>
            <hr></hr>
            {org.parentOrganization && org.parentOrganization.length > 0 && (
              <div style={{ marginTop: '0.5rem', marginBottom: '0.5rem' }}>
                <strong>Parent:</strong>
                <span style={{ marginLeft: '8px', color: '#A9A9A9', cursor: 'pointer', userSelect: 'none' }} onClick={() => copyToClipboard(org.parentOrganization[0].hiveId)} title="Copy Parent ID">
                   {org.parentOrganization[0].name} ({org.parentOrganization[0].hiveId})
                   {copiedId === org.parentOrganization[0].hiveId && ' ✓'}
                </span>
              </div>
            )}
            {org.childOrganizations && org.childOrganizations.length > 0 && (
              <div style={{ marginTop: '0.5rem', marginBottom: '1rem' }}>
                <strong>Children:</strong>
                <ul style={{ marginTop: '0.25rem', paddingLeft: '20px', listStyleType: 'disc' }}>
                  {org.childOrganizations.map(child => (
                    <li key={child.hiveId} style={{ color: '#A9A9A9' }}>
                      <span style={{cursor: 'pointer', userSelect: 'none' }} onClick={() => copyToClipboard(child.hiveId)} title="Copy Child ID">
                        {child.name} ({child.hiveId})
                        {copiedId === child.hiveId && ' ✓'}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
            <div style={{ display: 'flex', gap: '1rem' }}>
              <div style={{ 
                flex: 1,
                padding: '1rem',
                backgroundColor: '#2a2a2a',
                borderRadius: '4px'
              }}>
                <div>
                  <strong>Founded:</strong> <p style={{color: '#909090'}}>{org.foundingDate ? new Date(org.foundingDate).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: '2-digit' }) : 'Not specified'}</p>
                </div>
              </div>
              <div style={{ 
                flex: 1,
                padding: '1rem',
                backgroundColor: '#2a2a2a',
                borderRadius: '4px'
              }}>
                <div>
                  <strong>Owned Vehicles:</strong>
                  {org.ownsVehicle && org.ownsVehicle.length > 0 ? (
                    <ul>
                      {org.ownsVehicle.map(vehicle => (
                        <li style={{marginLeft: '18px', color: '#B0E0E6'}} key={vehicle.hiveId}>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            {`${vehicle.make} ${vehicle.model} (${vehicle.type}, ${vehicle.color})`}
                            <span 
                              style={{fontSize: '0.8em',color: '#909090',cursor: 'pointer',userSelect: 'none'}}
                              onClick={() => copyToClipboard(vehicle.hiveId)}
                              title="Copy vehicle ID"
                            >
                              {vehicle.hiveId}
                              {copiedId === vehicle.hiveId && ' ✓'}
                            </span>
                          </div>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p style={{color: '#909090'}}>No vehicles owned</p>
                  )}
                </div>
              </div>
              <div style={{ 
                flex: 1,
                padding: '1rem',
                backgroundColor: '#2a2a2a',
                borderRadius: '4px'
              }}>
                <div>
                  <strong>Members:</strong>
                  {org.members.length > 0 ? (
                    <ul>
                      {org.members.map(member => (
                        <li style={{marginLeft: '18px', color: '#ffda94'}} key={member.hiveId}>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            {`${member.firstName} ${member.lastName}`}
                            <span 
                              style={{fontSize: '0.8em',color: '#909090',cursor: 'pointer',userSelect: 'none'}}
                              onClick={() => copyToClipboard(member.hiveId)}
                              title="Click to copy Hive ID"
                            >
                              {member.hiveId}
                              {copiedId === member.hiveId && ' ✓'}
                            </span>
                          </div>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p style={{color: '#909090'}}>No members</p>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
