import React, { Suspense, lazy } from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import { ApolloProvider } from '@apollo/client';

import client from '../api/apolloClient';
import Sidebar from './components/Sidebar/Sidebar';
import Header from './components/Header/Header';
import Toast from './components/Reusables/Toast/Toast';
import { AuthProvider } from './components/Auth/AuthContext';
import ProtectedRoute from './components/Auth/ProtectedRoute';

const Home = lazy(() => import('./pages/Home/Home'));
const Settings = lazy(() => import('./pages/Settings/Settings'));
const Organizations = lazy(() => import('./pages/Organizations/Organizations'));
const People = lazy(() => import('./pages/People/People'));
const Cases = lazy(() => import('./pages/Cases/Cases'));
const Agents = lazy(() => import('./pages/Agents/Agents'));
const Informants = lazy(() => import('./pages/Informants/Informants'));
const Vehicles = lazy(() => import('./pages/Vehicles/Vehicles'));
const Operations = lazy(() => import('./pages/Operations/Operations'));
const Login = lazy(() => import('./components/Auth/Checkpoint'));

function AuthenticatedLayout({ children }) {
  return (
    <div className="app-container">
      <Sidebar />
      <div className="content">
        <Header />
        {children}
      </div>
    </div>
  );
}

export default function App() {
  if (process.env.NODE_ENV === 'development') {
    console.info("[Keystone] Session Ready in Development Mode.");
  }

  return (
    <ApolloProvider client={client}>
      <AuthProvider>
        <BrowserRouter future={{ v7_relativeSplatPath: true, v7_startTransition: true }}>
          <Toast />
          <Suspense fallback={<div>Loading...</div>}>
            <Routes>
              
              <Route path="/login" element={<Login />} />
              <Route path="/" element={
                <ProtectedRoute>
                  <AuthenticatedLayout>
                    <Home />
                  </AuthenticatedLayout>
                </ProtectedRoute>
              } />
              
              <Route path="/organizations" element={
                <ProtectedRoute>
                  <AuthenticatedLayout>
                    <Organizations />
                  </AuthenticatedLayout>
                </ProtectedRoute>
              } />
              
              <Route path="/people" element={
                <ProtectedRoute>
                  <AuthenticatedLayout>
                    <People />
                  </AuthenticatedLayout>
                </ProtectedRoute>
              } />
              
              <Route path="/cases" element={
                <ProtectedRoute>
                  <AuthenticatedLayout>
                    <Cases />
                  </AuthenticatedLayout>
                </ProtectedRoute>
              } />
              
              <Route path="/agents" element={
                <ProtectedRoute>
                  <AuthenticatedLayout>
                    <Agents />
                  </AuthenticatedLayout>
                </ProtectedRoute>
              } />
              
              <Route path="/informants" element={
                <ProtectedRoute>
                  <AuthenticatedLayout>
                    <Informants />
                  </AuthenticatedLayout>
                </ProtectedRoute>
              } />
              
              <Route path="/vehicles" element={
                <ProtectedRoute>
                  <AuthenticatedLayout>
                    <Vehicles />
                  </AuthenticatedLayout>
                </ProtectedRoute>
              } />

              <Route path="/operations" element={
                <ProtectedRoute>
                  <AuthenticatedLayout>
                    <Operations />
                  </AuthenticatedLayout>
                </ProtectedRoute>
              } />

              <Route path="/settings" element={
                <ProtectedRoute requireAdmin>
                  <AuthenticatedLayout>
                    <Settings />
                  </AuthenticatedLayout>
                </ProtectedRoute>
              } />
              
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </Suspense>
        </BrowserRouter>
      </AuthProvider>
    </ApolloProvider>
  );
} 
